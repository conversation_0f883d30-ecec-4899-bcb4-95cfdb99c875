"""
Simple configuration loader for FuzzLM-Agent Research Prototype
==============================================================

Just load YAML config file, no complex validation needed.
"""

from __future__ import annotations

import os
from pathlib import Path
from typing import Any

import yaml


def load_config(config_path: str = "config.yaml") -> dict[str, Any]:
    """Load configuration from YAML file.

    Args:
        config_path: Path to config file

    Returns:
        Configuration dictionary
    """
    # Try to load config file
    config_file = Path(config_path)
    if config_file.exists():
        with config_file.open() as f:
            config = yaml.safe_load(f) or {}
    else:
        config = {}

    # Apply defaults for missing values
    defaults = {
        "llm": {
            "provider": "openrouter",
            "model": "anthropic/claude-3.5-sonnet",
            "api_key": os.getenv("OPENROUTER_API_KEY", ""),
            "temperature": 0.1,
            "max_retries": 3,
        },
        "grpc": {"address": "localhost:50051", "timeout": 30.0},
        "knowledge_base": {"path": "./data/knowledge_base.db"},
        "system": {
            "workspace_root": "./workspace",
            "default_duration_hours": 2.0,
            "shadow_coverage_threshold": 0.3,
            "cleanup_workspace": False,
        },
    }

    # Merge with defaults
    for key, default_value in defaults.items():
        if key not in config:
            config[key] = default_value
        elif isinstance(default_value, dict):
            # Merge nested dicts
            for subkey, subvalue in default_value.items():
                if subkey not in config[key]:
                    config[key][subkey] = subvalue

    return config


# For backward compatibility
class Config:
    """Simple config wrapper for backward compatibility."""

    def __init__(self, config_dict: dict[str, Any]) -> None:
        self._config = config_dict

    def get(
        self, key: str, default: dict[str, Any] | str | float | None = None
    ) -> dict[str, Any] | str | int | float | None:
        """Get config value."""
        result = self._config.get(key, default)
        # 确保返回值符合声明的类型
        if isinstance(result, (dict, str, int, float)) or result is None:
            return result
        # 如果类型不匹配，返回默认值
        return default
