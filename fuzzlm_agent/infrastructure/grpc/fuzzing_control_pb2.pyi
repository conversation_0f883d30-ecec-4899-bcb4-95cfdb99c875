# Type stub for fuzzing_control_pb2
# This file provides type annotations for the generated protobuf code

from enum import IntEnum
from typing import Any

class FuzzerType(IntEnum):
    CHAMPION: int
    SHADOW: int

class CompilationMode(IntEnum):
    DEBUG_MODE: int
    RELEASE_MODE: int
    AUTO_MODE: int

class HealthCheckRequest:
    def __init__(self, *, include_system_metrics: bool = False) -> None: ...
    include_system_metrics: bool

class HealthCheckResponse:
    def __init__(self) -> None: ...
    healthy: bool
    instances: dict[str, Any]
    system_metrics: Any

class StrategyConfig:
    def __init__(self, *, name: str = "", parameters: dict[str, str] = ...) -> None: ...
    name: str
    parameters: dict[str, str]

class StartFuzzerRequest:
    def __init__(
        self,
        *,
        instance_id: str = "",
        fuzzer_type: FuzzerType = ...,
        target_path: str = "",
        target_library_path: str = "",
        strategy: StrategyConfig | None = None,
    ) -> None: ...
    instance_id: str
    fuzzer_type: FuzzerType
    target_path: str
    target_library_path: str
    strategy: StrategyConfig | None

class StartFuzzerResponse:
    def __init__(self) -> None: ...
    success: bool
    error_message: str
    instance_id: str
    process_id: int

class StopFuzzerRequest:
    def __init__(self, *, instance_id: str = "", force: bool = False) -> None: ...
    instance_id: str
    force: bool

class StopFuzzerResponse:
    def __init__(self) -> None: ...
    success: bool
    error_message: str

class UpdateStrategyRequest:
    def __init__(
        self, *, instance_id: str = "", new_strategy: StrategyConfig | None = None
    ) -> None: ...
    instance_id: str
    new_strategy: StrategyConfig | None

class UpdateStrategyResponse:
    def __init__(self) -> None: ...
    success: bool
    error_message: str

class SpawnShadowRequest:
    def __init__(
        self,
        *,
        champion_id: str = "",
        shadow_id: str = "",
        shadow_strategy: StrategyConfig | None = None,
    ) -> None: ...
    champion_id: str
    shadow_id: str
    shadow_strategy: StrategyConfig | None

class SpawnShadowResponse:
    def __init__(self) -> None: ...
    success: bool
    error_message: str
    shadow_id: str
    process_id: int

class PromoteShadowRequest:
    def __init__(
        self,
        *,
        shadow_id: str = "",
        champion_id: str = "",
        merge_corpus: bool = False,
        terminate_champion: bool = False,
    ) -> None: ...
    shadow_id: str
    champion_id: str
    merge_corpus: bool
    terminate_champion: bool

class PromoteShadowResponse:
    def __init__(self) -> None: ...
    success: bool
    error_message: str
    new_champion_id: str

class ValidateCodeRequest:
    def __init__(
        self, *, code: str = "", language: str = "", validation_steps: list[str] = ...
    ) -> None: ...
    code: str
    language: str
    validation_steps: list[str]

class ValidateCodeResponse:
    def __init__(self) -> None: ...
    passed: bool
    results: list[Any]
    compiled_path: str

class ValidationResult:
    def __init__(self) -> None: ...
    step: str
    passed: bool
    message: str
    metrics: dict[str, float]

class InstanceStatus:
    def __init__(self) -> None: ...
    instance_id: str
    fuzzer_type: FuzzerType
    is_running: bool
    process_id: int
    uptime_seconds: int
    metrics: Any

class PerformanceMetrics:
    def __init__(self) -> None: ...
    executions_per_second: int
    coverage_edges: int
    crashes_found: int
    corpus_size: int

class SystemMetrics:
    def __init__(self) -> None: ...
    cpu_usage_percent: float
    memory_used_bytes: int
    memory_total_bytes: int
    disk_usage_percent: float

# Additional classes that might be needed
class GetChampionStateRequest:
    def __init__(
        self,
        *,
        champion_id: str = "",
        incremental: bool = False,
        compress: bool = False,
    ) -> None: ...

class GetChampionStateResponse:
    def __init__(self) -> None: ...
    success: bool
    error_message: str
    state_data: bytes
    is_compressed: bool
    is_incremental: bool
    state_size: int
    timestamp: int

class SyncIncrementalStateRequest:
    def __init__(
        self,
        *,
        instance_id: str = "",
        state_data: bytes = b"",
        is_compressed: bool = False,
    ) -> None: ...

class SyncIncrementalStateResponse:
    def __init__(self) -> None: ...
    success: bool
    error_message: str
    corpus_entries_added: int
    new_executions: int
    new_coverage: int
