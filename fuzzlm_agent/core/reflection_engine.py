"""
Reflection Engine for FuzzLM-Agent
基于LLM的反思学习引擎，用于分析fuzzing性能和策略效果

Phase 5: 反思学习与知识演化
"""

from __future__ import annotations

import json
import logging
from dataclasses import asdict, dataclass, field
from datetime import datetime, timezone
from enum import Enum
from typing import TYPE_CHECKING, Any

if TYPE_CHECKING:
    from fuzzlm_agent.core.statistical_analysis import PerformanceMetrics
    from fuzzlm_agent.infrastructure.litellm_client import LiteLLMClient

logger = logging.getLogger(__name__)


class ReflectionType(str, Enum):
    """反思类型枚举"""

    STATE_PERCEPTION = "state_perception"  # 状态感知反思
    STRATEGY_EFFECT = "strategy_effect"  # 策略效果反思
    BOTTLENECK_ANALYSIS = "bottleneck_analysis"  # 瓶颈分析反思
    KNOWLEDGE_EXTRACTION = "knowledge_extraction"  # 知识提炼反思
    FAILURE_ANALYSIS = "failure_analysis"  # 失败分析反思


@dataclass
class ReflectionContext:
    """反思上下文 - 包含反思所需的所有信息"""

    reflection_type: ReflectionType
    target_info: dict[str, Any]  # 目标程序信息
    performance_data: list[PerformanceMetrics]  # 性能数据
    state_analyses: list[dict[str, Any]] = field(
        default_factory=list
    )  # LLM状态分析历史
    strategy_history: list[dict[str, Any]] = field(default_factory=list)  # 策略历史
    event_description: str = ""  # 触发反思的事件描述
    metadata: dict[str, Any] = field(default_factory=dict)  # 额外元数据


@dataclass
class ReflectionResult:
    """反思结果"""

    reflection_id: str
    reflection_type: ReflectionType
    timestamp: datetime

    # 核心反思内容
    insights: list[str]  # 关键洞察
    learned_patterns: list[dict[str, Any]]  # 学到的模式
    heuristic_rules: list[dict[str, Any]]  # 提炼的启发式规则

    # 建议的行动
    recommended_actions: list[str]
    strategy_adjustments: dict[str, Any]

    # 知识条目 (用于存储到知识库)
    knowledge_entries: list[KnowledgeEntry] = field(default_factory=list)

    # 评估
    confidence: float = 0.0  # 反思置信度 (0-1)
    importance: float = 0.0  # 重要性评分 (0-1)

    def to_dict(self) -> dict[str, Any]:
        """转换为字典格式"""
        data = asdict(self)
        data["timestamp"] = self.timestamp.isoformat()
        data["knowledge_entries"] = [
            entry.to_dict() for entry in self.knowledge_entries
        ]
        return data


@dataclass
class KnowledgeEntry:
    """知识条目 - 可存储到知识库的结构化知识"""

    entry_type: str  # heuristic, pattern, strategy, lesson
    target_profile: dict[str, Any]  # 目标画像
    problem_context: str  # 问题情境
    solution_approach: str  # 解决方案
    evidence: dict[str, Any]  # 支撑证据
    effectiveness_score: float  # 有效性评分

    # 元数据
    created_at: datetime = field(default_factory=lambda: datetime.now(timezone.utc))
    tags: list[str] = field(default_factory=list)

    def to_dict(self) -> dict[str, Any]:
        """转换为字典格式"""
        data = asdict(self)
        data["created_at"] = self.created_at.isoformat()
        return data


class ReflectionEngine:
    """反思引擎 - 驱动LLM进行深度反思学习"""

    def __init__(self, llm_client: LiteLLMClient):
        self.llm = llm_client
        self.reflection_history: list[ReflectionResult] = []

    async def reflect(self, context: ReflectionContext) -> ReflectionResult:
        """执行反思"""
        logger.info(f"Starting reflection of type: {context.reflection_type}")

        # 根据反思类型选择合适的反思策略
        if context.reflection_type == ReflectionType.STATE_PERCEPTION:
            return await self._reflect_on_state_perception(context)
        if context.reflection_type == ReflectionType.STRATEGY_EFFECT:
            return await self._reflect_on_strategy_effect(context)
        if context.reflection_type == ReflectionType.BOTTLENECK_ANALYSIS:
            return await self._reflect_on_bottleneck(context)
        if context.reflection_type == ReflectionType.KNOWLEDGE_EXTRACTION:
            return await self._reflect_on_knowledge(context)
        if context.reflection_type == ReflectionType.FAILURE_ANALYSIS:
            return await self._reflect_on_failure(context)
        msg = f"Unknown reflection type: {context.reflection_type}"
        raise ValueError(msg)

    async def _reflect_on_state_perception(
        self, context: ReflectionContext
    ) -> ReflectionResult:
        """状态感知反思 - 学习如何更准确地判断fuzzing状态"""
        # 准备性能数据摘要
        perf_summary = self._summarize_performance(context.performance_data)

        # 构建反思提示
        prompt = f"""# State Perception Reflection

You previously analyzed fuzzing telemetry data and made state judgments. Now reflect on your analysis accuracy.

## Target Information
{json.dumps(context.target_info, indent=2)}

## Performance Summary
{json.dumps(perf_summary, indent=2)}

## Your Previous State Analyses
{json.dumps(context.state_analyses[-3:], indent=2)}  # 最近3次分析

## Event That Triggered This Reflection
{context.event_description}

## Reflection Tasks

1. **Accuracy Assessment**: How accurate were your state judgments? Which were correct/incorrect?

2. **Pattern Recognition**: What patterns in the telemetry data correlate with specific fuzzing states?

3. **Heuristic Extraction**: Extract 1-3 specific heuristic rules for quickly identifying fuzzing states for programs similar to this target.

4. **Improvement Suggestions**: How should you adjust your attention weights on different metrics for better state perception?

Output your reflection in the following JSON format:
{{
    "insights": ["insight1", "insight2", ...],
    "learned_patterns": [
        {{
            "pattern_name": "name",
            "description": "what pattern indicates what state",
            "telemetry_indicators": ["indicator1", "indicator2"],
            "confidence": 0.8
        }}
    ],
    "heuristic_rules": [
        {{
            "rule_name": "name",
            "condition": "when X metric shows Y pattern",
            "conclusion": "the fuzzer is likely in Z state",
            "applicable_targets": ["target characteristics"]
        }}
    ],
    "metric_weight_adjustments": {{
        "execution_rate": 0.2,
        "path_discovery_rate": 0.4,
        "coverage_growth": 0.3,
        "crash_rate": 0.1
    }},
    "confidence": 0.85
}}"""

        # 调用LLM进行反思
        response = self.llm.generate(prompt)
        reflection_data = self._parse_json_response(response)

        # 构建反思结果
        result = ReflectionResult(
            reflection_id=f"ref_{datetime.now(timezone.utc).strftime('%Y%m%d_%H%M%S')}",
            reflection_type=context.reflection_type,
            timestamp=datetime.now(timezone.utc),
            insights=reflection_data.get("insights", []),
            learned_patterns=reflection_data.get("learned_patterns", []),
            heuristic_rules=reflection_data.get("heuristic_rules", []),
            recommended_actions=[],
            strategy_adjustments={
                "metric_weights": reflection_data.get("metric_weight_adjustments", {})
            },
            confidence=reflection_data.get("confidence", 0.5),
            importance=0.8,  # 状态感知很重要
        )

        # 生成知识条目
        for rule in result.heuristic_rules:
            entry = KnowledgeEntry(
                entry_type="heuristic",
                target_profile=context.target_info,
                problem_context="state_perception",
                solution_approach=rule.get("condition", ""),
                evidence={"rule": rule, "performance": perf_summary},
                effectiveness_score=result.confidence,
                tags=[
                    "state_perception",
                    context.target_info.get("language", "unknown"),
                ],
            )
            result.knowledge_entries.append(entry)

        self.reflection_history.append(result)
        return result

    async def _reflect_on_strategy_effect(
        self, context: ReflectionContext
    ) -> ReflectionResult:
        """策略效果反思 - 分析策略变更的实际效果"""
        # 获取策略变更前后的性能对比
        if len(context.strategy_history) < 2:
            logger.warning("Not enough strategy history for effect analysis")
            return self._create_empty_result(context.reflection_type)

        prev_strategy = context.strategy_history[-2]
        curr_strategy = context.strategy_history[-1]

        # 性能变化分析
        perf_before = self._get_performance_window(
            context.performance_data, prev_strategy.get("start_time")
        )
        perf_after = self._get_performance_window(
            context.performance_data, curr_strategy.get("start_time")
        )

        prompt = f"""# Strategy Effect Reflection

Analyze the effect of strategy changes on fuzzing performance.

## Target Information
{json.dumps(context.target_info, indent=2)}

## Previous Strategy
{json.dumps(prev_strategy, indent=2)}

## Current Strategy
{json.dumps(curr_strategy, indent=2)}

## Performance Before Change
{json.dumps(self._summarize_performance(perf_before), indent=2)}

## Performance After Change
{json.dumps(self._summarize_performance(perf_after), indent=2)}

## Reflection Tasks

1. **Effect Analysis**: What was the actual impact of the strategy change? Did it achieve expected improvements?

2. **Success Factors**: If successful, what were the key factors? Which components contributed most?

3. **Failure Analysis**: If unsuccessful, what went wrong? What assumptions were incorrect?

4. **Generalization**: Extract generalizable knowledge about what strategy adjustments work for what situations.

Output JSON:
{{
    "insights": ["insight1", "insight2"],
    "effect_analysis": {{
        "overall_impact": "positive/negative/neutral",
        "improvement_percentage": 25.5,
        "key_metrics_changed": ["metric1", "metric2"]
    }},
    "success_factors": ["factor1", "factor2"],
    "failure_reasons": ["reason1"],
    "learned_patterns": [
        {{
            "pattern": "when to use what strategy",
            "context": "situation description",
            "expected_benefit": "what improvement to expect"
        }}
    ],
    "recommendations": ["recommendation1", "recommendation2"],
    "confidence": 0.8
}}"""

        response = self.llm.generate(prompt)
        reflection_data = self._parse_json_response(response)

        result = ReflectionResult(
            reflection_id=f"ref_{datetime.now(timezone.utc).strftime('%Y%m%d_%H%M%S')}",
            reflection_type=context.reflection_type,
            timestamp=datetime.now(timezone.utc),
            insights=reflection_data.get("insights", []),
            learned_patterns=reflection_data.get("learned_patterns", []),
            heuristic_rules=[],
            recommended_actions=reflection_data.get("recommendations", []),
            strategy_adjustments={},
            confidence=reflection_data.get("confidence", 0.5),
            importance=0.9,  # 策略效果分析非常重要
        )

        # 生成策略知识条目
        if (
            reflection_data.get("effect_analysis", {}).get("overall_impact")
            == "positive"
        ):
            entry = KnowledgeEntry(
                entry_type="strategy",
                target_profile=context.target_info,
                problem_context=prev_strategy.get(
                    "problem_addressed", "performance_improvement"
                ),
                solution_approach=json.dumps(curr_strategy.get("components", {})),
                evidence={
                    "performance_gain": reflection_data.get("effect_analysis", {}).get(
                        "improvement_percentage", 0
                    ),
                    "key_factors": reflection_data.get("success_factors", []),
                },
                effectiveness_score=min(
                    reflection_data.get("effect_analysis", {}).get(
                        "improvement_percentage", 0
                    )
                    / 100,
                    1.0,
                ),
                tags=[
                    "strategy",
                    "successful",
                    context.target_info.get("language", "unknown"),
                ],
            )
            result.knowledge_entries.append(entry)

        self.reflection_history.append(result)
        return result

    async def _reflect_on_bottleneck(
        self, context: ReflectionContext
    ) -> ReflectionResult:
        """瓶颈分析反思"""
        perf_summary = self._summarize_performance(context.performance_data)

        prompt = f"""# Bottleneck Analysis Reflection

Analyze performance bottlenecks in the fuzzing campaign.

## Target Information
{json.dumps(context.target_info, indent=2)}

## Performance Trends
{json.dumps(perf_summary, indent=2)}

## Recent State Analyses
{json.dumps(context.state_analyses[-2:], indent=2)}

## Analysis Tasks

1. **Identify Bottlenecks**: What are the main performance limiters?
2. **Root Causes**: What causes these bottlenecks?
3. **Solution Strategies**: How to address each bottleneck?
4. **Priority Ranking**: Which bottlenecks to address first?

Output JSON:
{{
    "insights": ["insight1"],
    "bottlenecks": [
        {{
            "type": "coverage_plateau/execution_speed/crash_discovery",
            "severity": "high/medium/low",
            "root_causes": ["cause1", "cause2"],
            "indicators": ["indicator1"],
            "suggested_solutions": ["solution1", "solution2"]
        }}
    ],
    "priority_actions": ["action1", "action2"],
    "confidence": 0.75
}}"""

        response = self.llm.generate(prompt)
        reflection_data = self._parse_json_response(response)

        result = ReflectionResult(
            reflection_id=f"ref_{datetime.now(timezone.utc).strftime('%Y%m%d_%H%M%S')}",
            reflection_type=context.reflection_type,
            timestamp=datetime.now(timezone.utc),
            insights=reflection_data.get("insights", []),
            learned_patterns=[],
            heuristic_rules=[],
            recommended_actions=reflection_data.get("priority_actions", []),
            strategy_adjustments={},
            confidence=reflection_data.get("confidence", 0.5),
            importance=0.85,
        )

        # 为每个严重瓶颈创建知识条目
        for bottleneck in reflection_data.get("bottlenecks", []):
            if bottleneck.get("severity") == "high":
                entry = KnowledgeEntry(
                    entry_type="pattern",
                    target_profile=context.target_info,
                    problem_context=f"bottleneck_{bottleneck.get('type', 'unknown')}",
                    solution_approach=json.dumps(
                        bottleneck.get("suggested_solutions", [])
                    ),
                    evidence={"bottleneck": bottleneck, "performance": perf_summary},
                    effectiveness_score=0.0,  # 待验证
                    tags=["bottleneck", bottleneck.get("type", "unknown")],
                )
                result.knowledge_entries.append(entry)

        self.reflection_history.append(result)
        return result

    async def _reflect_on_knowledge(
        self, context: ReflectionContext
    ) -> ReflectionResult:
        """知识提炼反思 - 从整个campaign中提炼可复用知识"""
        # 这是最高级的反思，通常在campaign结束时进行
        perf_summary = self._summarize_performance(context.performance_data)

        prompt = f"""# Knowledge Extraction Reflection

Extract reusable knowledge from this fuzzing campaign.

## Campaign Overview
- Target: {json.dumps(context.target_info, indent=2)}
- Duration: {len(context.performance_data)} time windows
- Total Strategies Tried: {len(context.strategy_history)}

## Performance Summary
{json.dumps(perf_summary, indent=2)}

## Strategy Evolution
{json.dumps([{"name": s.get("name"), "effect": s.get("effect_summary")} for s in context.strategy_history], indent=2)}

## Extraction Tasks

1. **Key Learnings**: What are the most important lessons from this campaign?
2. **Reusable Patterns**: What patterns can be applied to similar targets?
3. **Anti-patterns**: What should be avoided?
4. **Target Characterization**: How to quickly identify similar targets?

Output JSON:
{{
    "campaign_summary": "brief summary",
    "key_learnings": [
        {{
            "learning": "what was learned",
            "evidence": "supporting evidence",
            "applicability": "when this applies"
        }}
    ],
    "reusable_patterns": [
        {{
            "pattern_name": "name",
            "description": "detailed description",
            "when_to_use": "conditions",
            "expected_benefit": "what to expect"
        }}
    ],
    "anti_patterns": [
        {{
            "pattern": "what to avoid",
            "reason": "why",
            "alternative": "what to do instead"
        }}
    ],
    "target_characteristics": {{
        "key_features": ["feature1", "feature2"],
        "effective_strategies": ["strategy1"],
        "ineffective_strategies": ["strategy2"]
    }},
    "confidence": 0.9
}}"""

        response = self.llm.generate(prompt)
        reflection_data = self._parse_json_response(response)

        result = ReflectionResult(
            reflection_id=f"ref_{datetime.now(timezone.utc).strftime('%Y%m%d_%H%M%S')}",
            reflection_type=context.reflection_type,
            timestamp=datetime.now(timezone.utc),
            insights=[
                learning.get("learning", "")
                for learning in reflection_data.get("key_learnings", [])
            ],
            learned_patterns=reflection_data.get("reusable_patterns", []),
            heuristic_rules=[],
            recommended_actions=[],
            strategy_adjustments={},
            confidence=reflection_data.get("confidence", 0.5),
            importance=1.0,  # 知识提炼最重要
        )

        # 创建综合知识条目
        entry = KnowledgeEntry(
            entry_type="lesson",
            target_profile=context.target_info,
            problem_context="campaign_summary",
            solution_approach=json.dumps(
                {
                    "effective_strategies": reflection_data.get(
                        "target_characteristics", {}
                    ).get("effective_strategies", []),
                    "key_patterns": [
                        p.get("pattern_name")
                        for p in reflection_data.get("reusable_patterns", [])
                    ],
                }
            ),
            evidence={
                "campaign_performance": perf_summary,
                "learnings": reflection_data.get("key_learnings", []),
            },
            effectiveness_score=0.8,
            tags=[
                "campaign_summary",
                "comprehensive",
                context.target_info.get("language", "unknown"),
            ],
        )
        result.knowledge_entries.append(entry)

        self.reflection_history.append(result)
        return result

    async def _reflect_on_failure(self, context: ReflectionContext) -> ReflectionResult:
        """失败分析反思"""
        # 分析失败的策略或检测到的问题
        failure_description = context.metadata.get(
            "failure_description", "Unknown failure"
        )

        prompt = f"""# Failure Analysis Reflection

Analyze a failure in the fuzzing campaign.

## Failure Description
{failure_description}

## Context
- Target: {json.dumps(context.target_info, indent=2)}
- Recent Performance: {json.dumps(self._summarize_performance(context.performance_data[-5:]), indent=2)}
- Event: {context.event_description}

## Analysis Tasks

1. **Root Cause**: What caused the failure?
2. **Warning Signs**: What indicators preceded the failure?
3. **Prevention**: How to prevent similar failures?
4. **Recovery**: Best recovery strategies?

Output JSON:
{{
    "insights": ["insight1"],
    "root_causes": ["cause1", "cause2"],
    "warning_indicators": ["indicator1"],
    "prevention_measures": ["measure1"],
    "recovery_strategies": ["strategy1"],
    "lessons_learned": ["lesson1"],
    "confidence": 0.7
}}"""

        response = self.llm.generate(prompt)
        reflection_data = self._parse_json_response(response)

        result = ReflectionResult(
            reflection_id=f"ref_{datetime.now(timezone.utc).strftime('%Y%m%d_%H%M%S')}",
            reflection_type=context.reflection_type,
            timestamp=datetime.now(timezone.utc),
            insights=reflection_data.get("insights", []),
            learned_patterns=[],
            heuristic_rules=[],
            recommended_actions=reflection_data.get("recovery_strategies", []),
            strategy_adjustments={},
            confidence=reflection_data.get("confidence", 0.5),
            importance=0.8,
        )

        # 创建失败模式知识条目
        entry = KnowledgeEntry(
            entry_type="pattern",
            target_profile=context.target_info,
            problem_context=f"failure_{context.metadata.get('failure_type', 'unknown')}",
            solution_approach=json.dumps(
                {
                    "prevention": reflection_data.get("prevention_measures", []),
                    "recovery": reflection_data.get("recovery_strategies", []),
                }
            ),
            evidence={
                "failure": failure_description,
                "root_causes": reflection_data.get("root_causes", []),
            },
            effectiveness_score=0.0,
            tags=["failure", "anti_pattern"],
        )
        result.knowledge_entries.append(entry)

        self.reflection_history.append(result)
        return result

    def _summarize_performance(
        self, metrics_list: list[PerformanceMetrics]
    ) -> dict[str, Any]:
        """生成性能数据摘要"""
        if not metrics_list:
            return {"status": "no_data"}

        return {
            "window_count": len(metrics_list),
            "total_executions": sum(m.total_executions for m in metrics_list),
            "avg_execution_rate": sum(m.execution_rate for m in metrics_list)
            / len(metrics_list),
            "total_crashes": sum(m.crashes_found for m in metrics_list),
            "max_coverage": max(m.unique_edges for m in metrics_list),
            "coverage_growth": (
                metrics_list[-1].unique_edges - metrics_list[0].unique_edges
                if len(metrics_list) > 1
                else 0
            ),
            "avg_efficiency": sum(m.efficiency_score for m in metrics_list)
            / len(metrics_list),
            "trends": {
                "execution_rate": self._calculate_trend(
                    [m.execution_rate for m in metrics_list]
                ),
                "coverage": self._calculate_trend(
                    [m.unique_edges for m in metrics_list]
                ),
                "efficiency": self._calculate_trend(
                    [m.efficiency_score for m in metrics_list]
                ),
            },
        }

    def _calculate_trend(self, values: list[float]) -> str:
        """计算数值趋势"""
        if len(values) < 3:
            return "insufficient_data"

        # 简单线性趋势
        first_third = sum(values[: len(values) // 3]) / (len(values) // 3)
        last_third = sum(values[-len(values) // 3 :]) / (len(values) // 3)

        change_rate = (last_third - first_third) / (first_third + 0.001)  # 避免除零

        if change_rate > 0.1:
            return "increasing"
        if change_rate < -0.1:
            return "decreasing"
        return "stable"

    def _get_performance_window(
        self,
        all_metrics: list[PerformanceMetrics],
        start_time: datetime | None,
        window_minutes: int = 30,
    ) -> list[PerformanceMetrics]:
        """获取特定时间窗口的性能数据"""
        if not start_time or not all_metrics:
            return (
                all_metrics[-window_minutes:]
                if len(all_metrics) > window_minutes
                else all_metrics
            )

        # 找到开始时间后的数据
        result = []
        for m in all_metrics:
            if m.window_start >= start_time:
                result.append(m)
                if len(result) >= window_minutes:
                    break

        return result

    def _parse_json_response(self, response: str) -> dict[str, Any]:
        """解析LLM的JSON响应"""
        import json_repair

        try:
            # 使用json_repair处理可能格式不完美的JSON
            parsed = json_repair.loads(response)
            # 确保返回dict类型
            if isinstance(parsed, dict):
                return parsed
            logger.warning(f"LLM response is not a dict: {type(parsed)}")
            return {}
        except Exception as e:
            logger.error(f"Failed to parse LLM response: {e}")
            logger.debug(f"Raw response: {response}")
            return {}

    def _create_empty_result(self, reflection_type: ReflectionType) -> ReflectionResult:
        """创建空的反思结果"""
        return ReflectionResult(
            reflection_id=f"ref_{datetime.now(timezone.utc).strftime('%Y%m%d_%H%M%S')}",
            reflection_type=reflection_type,
            timestamp=datetime.now(timezone.utc),
            insights=[],
            learned_patterns=[],
            heuristic_rules=[],
            recommended_actions=[],
            strategy_adjustments={},
            knowledge_entries=[],
            confidence=0.0,
            importance=0.0,
        )
