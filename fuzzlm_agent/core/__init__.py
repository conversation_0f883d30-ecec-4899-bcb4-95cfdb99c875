"""Core analysis and processing components for FuzzLM-Agent.

This module contains the fundamental components for:
- Statistical analysis of telemetry data
- Reflection engine for strategy adaptation
- System integration validation
"""

from .reflection_engine import (
    KnowledgeEntry,
    ReflectionContext,
    ReflectionEngine,
    ReflectionResult,
    ReflectionType,
)
from .statistical_analysis import (
    PerformanceMetrics,
    StatisticalSummary,
    TelemetryAggregator,
    TelemetryAnalyzer,
)
from .system_integration_validator import (
    ComponentHealth,
    ComponentStatus,
    SystemIntegrationValidator,
    ValidationLevel,
    ValidationResult,
)

__all__ = [
    "ComponentHealth",
    "ComponentStatus",
    "KnowledgeEntry",
    "PerformanceMetrics",
    "ReflectionContext",
    # Reflection Engine
    "ReflectionEngine",
    "ReflectionResult",
    "ReflectionType",
    "StatisticalSummary",
    # System Integration
    "SystemIntegrationValidator",
    "TelemetryAggregator",
    # Statistical Analysis
    "TelemetryAnalyzer",
    "ValidationLevel",
    "ValidationResult",
]
