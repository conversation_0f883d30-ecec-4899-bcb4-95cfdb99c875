"""
System Integration Validator for FuzzLM-Agent
验证系统各组件之间的集成正确性

Phase 0 & Phase 2: 系统集成验证
"""

from __future__ import annotations

import asyncio
import logging
from dataclasses import dataclass, field
from datetime import datetime, timezone
from enum import Enum
from typing import TYPE_CHECKING, Any

if TYPE_CHECKING:
    from fuzzlm_agent.infrastructure.runtime_client import RuntimeClient
    from fuzzlm_agent.infrastructure.shared_memory.telemetry_stream import (
        TelemetryDataPlane,
    )

logger = logging.getLogger(__name__)


class ComponentStatus(str, Enum):
    """组件状态枚举"""

    HEALTHY = "healthy"
    DEGRADED = "degraded"
    FAILED = "failed"
    UNKNOWN = "unknown"


class ValidationLevel(str, Enum):
    """验证级别"""

    BASIC = "basic"  # 基础连接性测试
    FUNCTIONAL = "functional"  # 功能性测试
    PERFORMANCE = "performance"  # 性能测试
    STRESS = "stress"  # 压力测试


@dataclass
class ComponentHealth:
    """组件健康状态"""

    name: str
    status: ComponentStatus
    last_check: datetime
    latency_ms: float = 0.0
    error_count: int = 0
    success_rate: float = 1.0
    details: dict[str, Any] = field(default_factory=dict)


@dataclass
class ValidationResult:
    """验证结果"""

    timestamp: datetime
    level: ValidationLevel
    overall_status: ComponentStatus
    components: list[ComponentHealth]
    issues: list[str] = field(default_factory=list)
    warnings: list[str] = field(default_factory=list)
    recommendations: list[str] = field(default_factory=list)
    performance_metrics: dict[str, Any] = field(default_factory=dict)


class SystemIntegrationValidator:
    """系统集成验证器"""

    def __init__(
        self,
        runtime_client: RuntimeClient | None = None,
        telemetry_dataplane: TelemetryDataPlane | None = None,
    ):
        self.runtime_client = runtime_client
        self.telemetry_dataplane = telemetry_dataplane
        self.validation_history: list[ValidationResult] = []

    async def validate_system(
        self, level: ValidationLevel = ValidationLevel.BASIC, timeout: float = 30.0
    ) -> ValidationResult:
        """执行系统验证"""
        logger.info(f"Starting system validation at level: {level}")

        start_time = datetime.now(timezone.utc)
        components_health = []
        issues = []
        warnings = []
        recommendations = []

        # 1. 验证控制平面 (gRPC)
        control_plane_health = await self._validate_control_plane(level, timeout)
        components_health.append(control_plane_health)

        # 2. 验证数据平面 (共享内存)
        data_plane_health = await self._validate_data_plane(level, timeout)
        components_health.append(data_plane_health)

        # 3. 验证组件间通信
        if level in [
            ValidationLevel.FUNCTIONAL,
            ValidationLevel.PERFORMANCE,
            ValidationLevel.STRESS,
        ]:
            integration_health = await self._validate_integration(level, timeout)
            components_health.append(integration_health)

        # 4. 性能基准测试
        performance_metrics = {}
        if level in [ValidationLevel.PERFORMANCE, ValidationLevel.STRESS]:
            performance_metrics = await self._run_performance_tests(level)

        # 确定整体状态
        overall_status = self._determine_overall_status(components_health)

        # 生成问题和建议
        for component in components_health:
            if component.status == ComponentStatus.FAILED:
                issues.append(
                    f"{component.name} is in FAILED state: {component.details.get('error', 'Unknown error')}"
                )
            elif component.status == ComponentStatus.DEGRADED:
                warnings.append(
                    f"{component.name} is degraded: {component.details.get('reason', 'Unknown reason')}"
                )

        # 生成建议
        if overall_status != ComponentStatus.HEALTHY:
            recommendations.extend(self._generate_recommendations(components_health))

        # 创建验证结果
        result = ValidationResult(
            timestamp=start_time,
            level=level,
            overall_status=overall_status,
            components=components_health,
            issues=issues,
            warnings=warnings,
            recommendations=recommendations,
            performance_metrics=performance_metrics,
        )

        self.validation_history.append(result)

        logger.info(f"System validation completed. Overall status: {overall_status}")
        return result

    async def _validate_control_plane(
        self, level: ValidationLevel, timeout: float
    ) -> ComponentHealth:
        """验证控制平面 (gRPC)"""
        component_name = "Control Plane (gRPC)"

        if not self.runtime_client:
            return ComponentHealth(
                name=component_name,
                status=ComponentStatus.UNKNOWN,
                last_check=datetime.now(timezone.utc),
                details={"error": "Runtime client not configured"},
            )

        try:
            # 基础连接测试
            start_time = asyncio.get_event_loop().time()

            # 确保连接
            if not self.runtime_client._connected:
                await asyncio.wait_for(self.runtime_client.connect(), timeout=timeout)

            # 健康检查
            health_status = await asyncio.wait_for(
                self.runtime_client.health_check(), timeout=timeout
            )

            latency_ms = (asyncio.get_event_loop().time() - start_time) * 1000

            # 功能测试
            if level != ValidationLevel.BASIC:
                # 测试各个RPC接口
                test_results = await self._test_grpc_operations(level)
                success_rate = (
                    test_results["success_count"] / test_results["total_count"]
                )

                return ComponentHealth(
                    name=component_name,
                    status=(
                        ComponentStatus.HEALTHY
                        if success_rate > 0.95
                        else ComponentStatus.DEGRADED
                    ),
                    last_check=datetime.now(timezone.utc),
                    latency_ms=latency_ms,
                    success_rate=success_rate,
                    details={
                        "health_status": health_status,
                        "test_results": test_results,
                        "server_version": health_status.get("version", "unknown"),
                    },
                )
            # 基础测试只检查连接性
            return ComponentHealth(
                name=component_name,
                status=ComponentStatus.HEALTHY,
                last_check=datetime.now(timezone.utc),
                latency_ms=latency_ms,
                details={"health_status": health_status},
            )

        except asyncio.TimeoutError:
            return ComponentHealth(
                name=component_name,
                status=ComponentStatus.FAILED,
                last_check=datetime.now(timezone.utc),
                details={"error": f"Connection timeout after {timeout}s"},
            )
        except Exception as e:
            return ComponentHealth(
                name=component_name,
                status=ComponentStatus.FAILED,
                last_check=datetime.now(timezone.utc),
                details={"error": str(e)},
            )

    async def _validate_data_plane(
        self, level: ValidationLevel, timeout: float
    ) -> ComponentHealth:
        """验证数据平面 (共享内存)"""
        component_name = "Data Plane (Shared Memory)"

        if not self.telemetry_dataplane:
            return ComponentHealth(
                name=component_name,
                status=ComponentStatus.UNKNOWN,
                last_check=datetime.now(timezone.utc),
                details={"error": "Telemetry dataplane not configured"},
            )

        try:
            # 连接测试
            start_time = asyncio.get_event_loop().time()

            if not self.telemetry_dataplane.connected:
                connected = await asyncio.wait_for(
                    asyncio.to_thread(self.telemetry_dataplane.connect), timeout=timeout
                )
                if not connected:
                    msg = "Failed to connect to shared memory"
                    raise Exception(msg)

            # 读取测试
            test_reads = 0
            successful_reads = 0

            # 尝试读取多个条目
            for _ in range(10 if level == ValidationLevel.BASIC else 100):
                try:
                    entry = await self.telemetry_dataplane.read_entry(timeout=0.1)
                    test_reads += 1
                    if entry is not None:
                        successful_reads += 1
                except Exception:
                    test_reads += 1

            latency_ms = (asyncio.get_event_loop().time() - start_time) * 1000

            # 计算读取成功率
            read_success_rate = successful_reads / test_reads if test_reads > 0 else 0

            # 性能测试
            if level in [ValidationLevel.PERFORMANCE, ValidationLevel.STRESS]:
                perf_metrics = await self._test_dataplane_performance()

                return ComponentHealth(
                    name=component_name,
                    status=(
                        ComponentStatus.HEALTHY
                        if read_success_rate > 0.0 or test_reads == 0
                        else ComponentStatus.DEGRADED
                    ),
                    last_check=datetime.now(timezone.utc),
                    latency_ms=latency_ms,
                    success_rate=read_success_rate,
                    details={
                        "test_reads": test_reads,
                        "successful_reads": successful_reads,
                        "performance": perf_metrics,
                    },
                )
            # 基础/功能测试
            status = ComponentStatus.HEALTHY
            if not self.telemetry_dataplane.connected:
                status = ComponentStatus.FAILED
            elif read_success_rate == 0 and test_reads > 0:
                status = ComponentStatus.DEGRADED

            return ComponentHealth(
                name=component_name,
                status=status,
                last_check=datetime.now(timezone.utc),
                latency_ms=latency_ms,
                success_rate=read_success_rate,
                details={
                    "connected": self.telemetry_dataplane.connected,
                    "test_reads": test_reads,
                    "successful_reads": successful_reads,
                },
            )

        except Exception as e:
            return ComponentHealth(
                name=component_name,
                status=ComponentStatus.FAILED,
                last_check=datetime.now(timezone.utc),
                details={"error": str(e)},
            )

    async def _validate_integration(
        self, level: ValidationLevel, timeout: float
    ) -> ComponentHealth:
        """验证组件间集成"""
        component_name = "System Integration"

        try:
            issues = []
            successes = []

            # 1. 测试控制命令 -> 数据流
            if self.runtime_client and self.telemetry_dataplane:
                # 启动一个测试fuzzer
                test_result = await self._test_control_to_data_flow()
                if test_result["success"]:
                    successes.append("Control-to-data flow working")
                else:
                    issues.append(
                        f"Control-to-data flow failed: {test_result['error']}"
                    )

            # 2. 测试数据聚合
            if self.telemetry_dataplane:
                agg_result = await self._test_data_aggregation()
                if agg_result["success"]:
                    successes.append("Data aggregation working")
                else:
                    issues.append(f"Data aggregation failed: {agg_result['error']}")

            # 确定状态
            if not issues:
                status = ComponentStatus.HEALTHY
            elif len(issues) < len(successes):
                status = ComponentStatus.DEGRADED
            else:
                status = ComponentStatus.FAILED

            return ComponentHealth(
                name=component_name,
                status=status,
                last_check=datetime.now(timezone.utc),
                success_rate=(
                    len(successes) / (len(successes) + len(issues))
                    if successes or issues
                    else 0
                ),
                details={"successes": successes, "issues": issues},
            )

        except Exception as e:
            return ComponentHealth(
                name=component_name,
                status=ComponentStatus.FAILED,
                last_check=datetime.now(timezone.utc),
                details={"error": str(e)},
            )

    async def _test_grpc_operations(self, level: ValidationLevel) -> dict[str, Any]:
        """测试gRPC操作"""
        if not self.runtime_client:
            return {"total_count": 0, "success_count": 0}

        operations = [
            ("health_check", self.runtime_client.health_check),
        ]

        total_count = 0
        success_count = 0
        operation_results = {}

        for op_name, op_func in operations:
            total_count += 1
            try:
                result = await asyncio.wait_for(op_func(), timeout=5.0)
                success_count += 1
                operation_results[op_name] = {"success": True, "result": result}
            except Exception as e:
                operation_results[op_name] = {"success": False, "error": str(e)}

        return {
            "total_count": total_count,
            "success_count": success_count,
            "operations": operation_results,
        }

    async def _test_dataplane_performance(self) -> dict[str, Any]:
        """测试数据平面性能"""
        if not self.telemetry_dataplane:
            return {}

        # 批量读取测试
        batch_sizes = [10, 100, 1000]
        results = {}

        for batch_size in batch_sizes:
            start_time = asyncio.get_event_loop().time()
            entries = await self.telemetry_dataplane.read_batch(max_entries=batch_size)
            duration_ms = (asyncio.get_event_loop().time() - start_time) * 1000

            results[f"batch_{batch_size}"] = {
                "requested": batch_size,
                "received": len(entries),
                "duration_ms": duration_ms,
                "throughput": (
                    len(entries) / (duration_ms / 1000) if duration_ms > 0 else 0
                ),
            }

        return results

    async def _test_control_to_data_flow(self) -> dict[str, Any]:
        """测试控制命令到数据流的端到端流程"""
        # 这是一个简化的测试
        # 实际实现需要启动测试fuzzer并验证数据流
        return {"success": True, "latency_ms": 0}

    async def _test_data_aggregation(self) -> dict[str, Any]:
        """测试数据聚合功能"""
        # 测试是否能正确聚合遥测数据
        return {"success": True, "aggregation_rate": 1000}

    async def _run_performance_tests(self, level: ValidationLevel) -> dict[str, Any]:
        """运行性能测试"""
        metrics = {}

        # 控制平面延迟测试
        if self.runtime_client:
            latencies = []
            for _ in range(10):
                start = asyncio.get_event_loop().time()
                try:
                    await self.runtime_client.health_check()
                    latencies.append((asyncio.get_event_loop().time() - start) * 1000)
                except Exception:
                    pass

            if latencies:
                metrics["control_plane_latency"] = {
                    "avg_ms": sum(latencies) / len(latencies),
                    "min_ms": min(latencies),
                    "max_ms": max(latencies),
                }

        # 数据平面吞吐量测试
        if self.telemetry_dataplane:
            start = asyncio.get_event_loop().time()
            total_entries = 0

            # 读取1秒的数据
            while asyncio.get_event_loop().time() - start < 1.0:
                batch = await self.telemetry_dataplane.read_batch(max_entries=1000)
                total_entries += len(batch)
                if not batch:
                    await asyncio.sleep(0.01)

            metrics["data_plane_throughput"] = {
                "entries_per_second": total_entries,
                "test_duration_s": asyncio.get_event_loop().time() - start,
            }

        return metrics

    def _determine_overall_status(
        self, components: list[ComponentHealth]
    ) -> ComponentStatus:
        """确定整体系统状态"""
        if not components:
            return ComponentStatus.UNKNOWN

        # 如果任何关键组件失败，整体失败
        if any(c.status == ComponentStatus.FAILED for c in components):
            return ComponentStatus.FAILED

        # 如果多个组件降级，整体失败
        degraded_count = sum(
            1 for c in components if c.status == ComponentStatus.DEGRADED
        )
        if degraded_count > len(components) / 2:
            return ComponentStatus.FAILED

        # 如果有组件降级，整体降级
        if degraded_count > 0:
            return ComponentStatus.DEGRADED

        # 所有组件健康
        return ComponentStatus.HEALTHY

    def _generate_recommendations(self, components: list[ComponentHealth]) -> list[str]:
        """生成改进建议"""
        recommendations = []

        for component in components:
            if component.status == ComponentStatus.FAILED:
                if "timeout" in str(component.details.get("error", "")):
                    recommendations.append(
                        f"Check network connectivity for {component.name}"
                    )
                elif "connection" in str(component.details.get("error", "")):
                    recommendations.append(
                        f"Ensure the Rust fuzzing engine is running for {component.name}"
                    )

            elif component.status == ComponentStatus.DEGRADED:
                if component.success_rate < 0.5:
                    recommendations.append(
                        f"Investigate high failure rate in {component.name}"
                    )
                if component.latency_ms > 1000:
                    recommendations.append(
                        f"Optimize performance of {component.name} (high latency: {component.latency_ms:.0f}ms)"
                    )

        # 通用建议
        if not any(
            c.name == "Control Plane (gRPC)" and c.status == ComponentStatus.HEALTHY
            for c in components
        ):
            recommendations.append(
                "Start the Rust fuzzing engine with: cd fuzzing-engine && cargo run -- --grpc-address 127.0.0.1:50051"
            )

        return recommendations

    def get_validation_summary(self) -> dict[str, Any]:
        """获取验证历史摘要"""
        if not self.validation_history:
            return {"status": "no_validations_performed"}

        recent = self.validation_history[-1]

        return {
            "last_validation": recent.timestamp.isoformat(),
            "overall_status": recent.overall_status,
            "healthy_components": sum(
                1 for c in recent.components if c.status == ComponentStatus.HEALTHY
            ),
            "degraded_components": sum(
                1 for c in recent.components if c.status == ComponentStatus.DEGRADED
            ),
            "failed_components": sum(
                1 for c in recent.components if c.status == ComponentStatus.FAILED
            ),
            "total_validations": len(self.validation_history),
            "recent_issues": recent.issues,
            "recommendations": recent.recommendations,
        }
