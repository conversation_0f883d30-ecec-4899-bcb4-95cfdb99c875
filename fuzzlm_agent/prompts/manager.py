"""
Simplified Prompt Manager for Research Prototype
===============================================

Minimalist prompt management system for FuzzLM-Agent research prototype.
Focuses on core functionality: template loading and rendering.

Removed features for simplicity:
- LRU caching
- Usage statistics
- Custom filters
- Template validation
- Template listing
"""

from __future__ import annotations

import logging
from pathlib import Path
from typing import Any

import yaml
from jinja2 import Environment, FileSystemLoader

logger = logging.getLogger(__name__)


class PromptManager:
    """
    Simplified prompt management system for research prototype.

    Core functionality only: loads YAML templates and renders them with Jinja2.
    """

    def __init__(self, template_path: str | None = None):
        """
        Initialize the PromptManager.

        Args:
            template_path: Path to template directory. If None, uses default location.
        """
        self.template_path = (
            Path(template_path) if template_path else self._get_default_template_path()
        )

        # Initialize Jinja2 environment
        self.jinja_env = Environment(
            loader=FileSystemLoader(str(self.template_path)),
            trim_blocks=True,
            lstrip_blocks=True,
            autoescape=False,
        )

        logger.info(
            f"PromptManager initialized with templates from: {self.template_path}"
        )

    def _get_default_template_path(self) -> Path:
        """Get the default template directory path."""
        current_dir = Path(__file__).parent
        return current_dir / "templates"

    def get_prompt(self, template_name: str, **kwargs: Any) -> str:
        """
        Generate a prompt from a template with given parameters.

        Args:
            template_name: Name of the template (e.g., 'strategy.generation')
            **kwargs: Variables to substitute in the template

        Returns:
            Rendered prompt string

        Raises:
            FileNotFoundError: If template file doesn't exist
            Exception: If template rendering fails
        """
        try:
            # Load template configuration
            template_config = self._load_template_config(template_name)

            # Get template content
            template_content = template_config.get("template", "")
            if not template_content:
                msg = f"Template '{template_name}' has no content"
                raise ValueError(msg)

            # Create Jinja2 template
            template = self.jinja_env.from_string(template_content)

            # Merge template defaults with provided kwargs
            render_vars = {}
            render_vars.update(template_config.get("defaults", {}))
            render_vars.update(kwargs)

            # Render template
            rendered_prompt = template.render(**render_vars)

            logger.debug(f"Successfully rendered template '{template_name}'")
            return rendered_prompt

        except Exception as e:
            logger.error(f"Failed to render template '{template_name}': {e}")
            raise

    def _load_template_config(self, template_name: str) -> dict[str, Any]:
        """
        Load template configuration from YAML file.

        Args:
            template_name: Template name (e.g., 'strategy.generation')

        Returns:
            Template configuration dictionary
        """
        # Convert dot notation to file path (strategy.generation -> strategy/generation.yaml)
        template_parts = template_name.split(".")
        template_file = (
            self.template_path
            / "/".join(template_parts[:-1])
            / f"{template_parts[-1]}.yaml"
        )

        if not template_file.exists():
            msg = f"Template file not found: {template_file}"
            raise FileNotFoundError(msg)

        with open(template_file, encoding="utf-8") as f:
            config = yaml.safe_load(f)

        return config or {}
