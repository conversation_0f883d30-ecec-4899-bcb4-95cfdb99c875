#!/usr/bin/env python3
"""
安全隔离的LibAFL验证执行器
防止目标程序崩溃影响主进程, 提供完整的安全执行环境
"""

from __future__ import annotations

import contextlib
import json
import os
import queue
import re
import resource
import signal
import subprocess
import sys
import threading
import time
from dataclasses import asdict, dataclass
from pathlib import Path
from queue import Queue
from typing import Any


@dataclass
class SafeExecutionResult:
    """安全执行结果"""

    success: bool
    exit_code: int
    stdout: str
    stderr: str
    execution_time: float
    crash_detected: bool
    crash_signal: int | None
    timeout_occurred: bool
    resource_usage: dict[str, Any]
    corpus_generated: bool
    corpus_count: int
    error_analysis: dict[str, Any]


class SafeProcessExecutor:
    """安全进程执行器, 提供完整的隔离和监控"""

    def __init__(self, timeout_seconds: int = 30, memory_limit_mb: int = 1024) -> None:
        self.timeout_seconds = timeout_seconds
        self.memory_limit_mb = memory_limit_mb
        self.start_time: float | None = None
        self.process: subprocess.Popen[bytes] | None = None
        self.stdout_queue: Queue[str] = Queue()
        self.stderr_queue: Queue[str] = Queue()

    def _set_resource_limits(self) -> None:
        """轻量级资源限制 - 允许正常的Rust编译和执行"""
        try:
            # 只设置最基本的限制, 避免干扰Rust编译器
            # 不设置内存限制, 因为会导致标准库无法加载

            # 仅设置CPU时间限制作为安全措施
            cpu_soft = self.timeout_seconds + 15
            cpu_hard = self.timeout_seconds + 30
            resource.setrlimit(resource.RLIMIT_CPU, (cpu_soft, cpu_hard))

        except (OSError, ValueError):
            # 设置资源限制失败, 继续以无限制模式运行
            pass

    def _read_stream(
        self, process: subprocess.Popen[bytes], output_queue: Queue[str]
    ) -> None:
        """在单独线程中读取流输出"""
        try:
            if process.stdout is not None:
                for line in iter(process.stdout.readline, b""):
                    output_queue.put(line.decode("utf-8", errors="replace"))
                process.stdout.close()
        except (UnicodeDecodeError, OSError):
            # 流读取或编码错误时退出
            pass

    def _monitor_process(
        self, process: subprocess.Popen[bytes]
    ) -> tuple[bool, int | None, bool]:
        """监控进程执行, 检测崩溃和超时"""
        crash_detected = False
        crash_signal = None
        timeout_occurred = False

        try:
            # 等待进程完成或超时
            try:
                process.wait(timeout=self.timeout_seconds)
            except subprocess.TimeoutExpired:
                timeout_occurred = True
                # 进程超时, 正在终止

                # 尝试优雅终止
                process.terminate()
                try:
                    process.wait(timeout=5)
                except subprocess.TimeoutExpired:
                    # 强制杀死
                    process.kill()
                    process.wait()

            # 检查退出状态
            if process.returncode and process.returncode < 0:
                crash_signal = -process.returncode
                crash_detected = True

        except (OSError, ValueError, AttributeError):
            crash_detected = True

        return crash_detected, crash_signal, timeout_occurred

    def execute_command(
        self,
        cmd: list[str],
        cwd: str | None = None,
        env: dict[str, str] | None = None,
    ) -> SafeExecutionResult:
        """安全执行命令"""
        self.start_time = time.time()
        self._log_execution_info(cmd, cwd)

        exec_env = self._prepare_environment(env)
        stdout_data, stderr_data, crash_detected, crash_signal, timeout_occurred = (
            self._execute_process(cmd, cwd, exec_env)
        )

        return self._build_result(
            stdout_data,
            stderr_data,
            crash_detected,
            crash_signal,
            timeout_occurred,
        )

    def _log_execution_info(self, cmd: list[str], cwd: str | None) -> None:
        """记录执行信息"""
        # 记录执行信息到内部状态而不是打印
        _ = cmd, cwd  # 标记参数已使用

    def _prepare_environment(self, env: dict[str, str] | None) -> dict[str, str]:
        """准备执行环境"""
        exec_env = os.environ.copy()
        if env:
            exec_env.update(env)

        # 设置Rust日志级别为最小
        exec_env["RUST_LOG"] = "error"
        exec_env["RUST_BACKTRACE"] = "1"
        return exec_env

    def _execute_process(
        self,
        cmd: list[str],
        cwd: str | None,
        exec_env: dict[str, str],
    ) -> tuple[list[str], list[str], bool, int | None, bool]:
        """执行进程并收集输出"""
        stdout_data: list[str] = []
        stderr_data: list[str] = []
        crash_detected = False
        crash_signal = None
        timeout_occurred = False

        try:
            # 启动进程
            self.process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                cwd=cwd,
                env=exec_env,
                preexec_fn=self._set_resource_limits,
                start_new_session=True,  # 创建新的进程组
            )

            # 启动输出读取线程并监控进程
            stdout_thread, stderr_thread = self._start_output_threads()
            crash_detected, crash_signal, timeout_occurred = self._monitor_process(
                self.process,
            )

            # 等待线程完成并收集输出
            self._wait_for_threads(stdout_thread, stderr_thread)
            stdout_data = self._collect_queue_data(self.stdout_queue)
            stderr_data = self._collect_queue_data(self.stderr_queue)

        except (OSError, subprocess.SubprocessError) as e:
            stderr_data.append(f"执行异常: {e!s}\n")
            crash_detected = True

        finally:
            self._cleanup_process()

        return stdout_data, stderr_data, crash_detected, crash_signal, timeout_occurred

    def _start_output_threads(
        self,
    ) -> tuple[threading.Thread | None, threading.Thread | None]:
        """启动输出读取线程"""
        stdout_thread = None
        stderr_thread = None

        if self.process and self.process.stdout:
            stdout_thread = threading.Thread(
                target=self._read_stream,
                args=(self.process.stdout, self.stdout_queue),
            )
            stdout_thread.start()

        if self.process and self.process.stderr:
            stderr_thread = threading.Thread(
                target=self._read_stream,
                args=(self.process.stderr, self.stderr_queue),
            )
            stderr_thread.start()

        return stdout_thread, stderr_thread

    def _wait_for_threads(
        self,
        stdout_thread: threading.Thread | None,
        stderr_thread: threading.Thread | None,
    ) -> None:
        """等待读取线程完成"""
        if stdout_thread:
            stdout_thread.join(timeout=2)
        if stderr_thread:
            stderr_thread.join(timeout=2)

    def _collect_queue_data(self, data_queue: Queue[str]) -> list[str]:
        """从队列收集数据"""
        data = []
        # 避免在循环中使用try-except以提高性能
        while not data_queue.empty():
            try:
                item = data_queue.get_nowait()
                data.append(item)
            except queue.Empty:
                # 队列为空时退出
                break
        return data

    def _cleanup_process(self) -> None:
        """清理进程"""
        if self.process and self.process.poll() is None:
            with contextlib.suppress(OSError, ProcessLookupError):
                # 杀死整个进程组
                os.killpg(os.getpgid(self.process.pid), signal.SIGKILL)

    def _build_result(
        self,
        stdout_data: list[str],
        stderr_data: list[str],
        crash_detected: bool,
        crash_signal: int | None,
        timeout_occurred: bool,
    ) -> SafeExecutionResult:
        """构建执行结果"""
        execution_time = time.time() - (self.start_time or 0)
        stdout_str = "".join(stdout_data)
        stderr_str = "".join(stderr_data)

        # 计算资源使用情况
        resource_usage = self._get_resource_usage()

        # 分析结果
        success = (
            not crash_detected
            and not timeout_occurred
            and (self.process.returncode == 0 if self.process else False)
        )
        exit_code = self.process.returncode if self.process else -1

        # 分析语料库生成情况
        corpus_generated, corpus_count = self._analyze_corpus_generation(
            stdout_str,
            stderr_str,
        )

        # 错误分析
        error_analysis = self._analyze_errors(
            stdout_str,
            stderr_str,
            crash_detected=crash_detected,
            timeout_occurred=timeout_occurred,
        )

        return SafeExecutionResult(
            success=success,
            exit_code=exit_code,
            stdout=stdout_str,
            stderr=stderr_str,
            execution_time=execution_time,
            crash_detected=crash_detected,
            crash_signal=crash_signal,
            timeout_occurred=timeout_occurred,
            resource_usage=resource_usage,
            corpus_generated=corpus_generated,
            corpus_count=corpus_count,
            error_analysis=error_analysis,
        )

    def _get_resource_usage(self) -> dict[str, Any]:
        """获取资源使用情况"""
        try:
            usage = resource.getrusage(resource.RUSAGE_CHILDREN)
            return {
                "user_time": usage.ru_utime,
                "system_time": usage.ru_stime,
                "max_rss_kb": usage.ru_maxrss,
                "page_faults": usage.ru_majflt + usage.ru_minflt,
                "context_switches": usage.ru_nvcsw + usage.ru_nivcsw,
            }
        except (AttributeError, OSError):
            # 资源使用统计获取失败时返回空字典
            return {}

    def _analyze_corpus_generation(self, stdout: str, stderr: str) -> tuple[bool, int]:
        """分析语料库生成情况"""
        corpus_generated = False
        corpus_count = 0

        try:
            # 查找语料库相关信息
            combined_output = stdout + stderr

            if "语料库文件数:" in combined_output:
                corpus_generated = True
                # 尝试提取文件数
                match = re.search(r"语料库文件数:\s*(\d+)", combined_output)
                if match:
                    corpus_count = int(match.group(1))

            # 查找其他成功指标
            if "✓ 创建LibAFL fuzzer实例" in combined_output:
                corpus_generated = True

        except (ValueError, AttributeError, TypeError):
            # 语料库分析失败时跳过
            pass

        return corpus_generated, corpus_count

    def _analyze_errors(
        self,
        stdout: str,
        stderr: str,
        *,
        crash_detected: bool,
        timeout_occurred: bool,
    ) -> dict[str, Any]:
        """分析错误情况"""
        analysis: dict[str, Any] = {
            "critical_errors": [],
            "warnings": [],
            "fix_suggestions": [],
            "likely_causes": [],
        }

        combined_output = stdout + stderr

        # 查找关键错误
        if "EDGES_MAP" in combined_output:
            analysis["critical_errors"].append("EDGES_MAP冲突问题")
            analysis["fix_suggestions"].append("检查libafl_targets::EDGES_MAP的使用")

        if "OnDiskCorpus" in combined_output and "error" in combined_output.lower():
            analysis["critical_errors"].append("OnDiskCorpus配置问题")
            analysis["fix_suggestions"].append("验证磁盘语料库配置和权限")

        if crash_detected:
            analysis["critical_errors"].append("进程崩溃")
            analysis["likely_causes"].extend(
                ["目标程序执行时发生段错误", "LibAFL内部错误", "资源访问冲突"],
            )

        if timeout_occurred:
            analysis["warnings"].append("执行超时")
            analysis["likely_causes"].append("测试运行时间过长或死锁")

        # 查找编译错误
        if "error:" in stderr and "cargo" in stderr:
            analysis["critical_errors"].append("Rust编译错误")
            analysis["fix_suggestions"].append("修复Rust代码编译问题")

        return analysis


def print_detailed_report(result: SafeExecutionResult) -> None:
    """打印详细的执行报告"""
    separator_length = 60

    print("\n" + "=" * separator_length)
    print("🛡️ LibAFL安全验证执行报告")
    print("=" * separator_length)

    _print_basic_info(result)
    _print_corpus_analysis(result)
    _print_resource_usage(result)
    _print_error_analysis(result)
    _print_output_sections(result)


def _print_basic_info(result: SafeExecutionResult) -> None:
    """打印基本执行信息"""
    print(f"✅ 执行成功: {'是' if result.success else '否'}")
    print(f"🏁 退出代码: {result.exit_code}")
    print(f"⏱️ 执行时间: {result.execution_time:.2f}秒")
    print(f"💥 崩溃检测: {'是' if result.crash_detected else '否'}")
    if result.crash_signal:
        print(f"🚨 崩溃信号: {result.crash_signal}")
    print(f"⏰ 超时发生: {'是' if result.timeout_occurred else '否'}")


def _print_corpus_analysis(result: SafeExecutionResult) -> None:
    """打印语料库分析"""
    print("\n📁 语料库生成分析:")
    print(f"   生成状态: {'成功' if result.corpus_generated else '失败'}")
    print(f"   文件数量: {result.corpus_count}")


def _print_resource_usage(result: SafeExecutionResult) -> None:
    """打印资源使用情况"""
    if result.resource_usage:
        print("\n💾 资源使用情况:")
        for key, value in result.resource_usage.items():
            print(f"   {key}: {value}")


def _print_error_analysis(result: SafeExecutionResult) -> None:
    """打印错误分析"""
    if not result.error_analysis:
        return

    error_analysis = result.error_analysis

    _print_error_section("❌ 关键错误:", error_analysis.get("critical_errors", []))
    _print_error_section("⚠️ 警告:", error_analysis.get("warnings", []))
    _print_error_section("🔍 可能原因:", error_analysis.get("likely_causes", []))
    _print_error_section("💡 修复建议:", error_analysis.get("fix_suggestions", []))


def _print_error_section(title: str, items: list[str]) -> None:
    """打印错误分析的一个部分"""
    if items:
        print(f"\n{title}")
        for item in items:
            print(f"   • {item}")


def _print_output_sections(result: SafeExecutionResult) -> None:
    """打印输出部分"""
    output_preview_length = 500
    separator_length = 60

    # 输出摘要
    print(f"\n📝 标准输出 (前{output_preview_length}字符):")
    print("-" * 40)
    print(result.stdout[:output_preview_length])
    if len(result.stdout) > output_preview_length:
        print("... (截断)")

    print(f"\n📝 错误输出 (前{output_preview_length}字符):")
    print("-" * 40)
    print(result.stderr[:output_preview_length])
    if len(result.stderr) > output_preview_length:
        print("... (截断)")

    print("=" * separator_length)


def main() -> int:
    """主执行函数"""
    print("🛡️ LibAFL安全验证执行器启动")
    print("=" * 50)

    # 定位fuzzing-engine目录
    script_dir = Path(__file__).parent
    engine_dir = script_dir.parent

    if not engine_dir.exists() or not (engine_dir / "Cargo.toml").exists():
        print(f"❌ 找不到fuzzing-engine目录: {engine_dir}")
        return 1

    print(f"📁 工作目录: {engine_dir}")

    # 检查是否已经构建
    target_dir = engine_dir / "target" / "release"
    binary_path = target_dir / "verify-libafl"

    if not binary_path.exists():
        print("🔨 verify-libafl二进制文件不存在，尝试构建...")

        # 构建验证程序
        build_executor = SafeProcessExecutor(timeout_seconds=120, memory_limit_mb=1024)
        build_cmd = ["cargo", "build", "--bin", "verify-libafl", "--release"]

        print(f"🔨 执行构建命令: {' '.join(build_cmd)}")
        build_result = build_executor.execute_command(build_cmd, cwd=str(engine_dir))

        if not build_result.success:
            print("❌ 构建失败!")
            print_detailed_report(build_result)
            return 1

        print("✅ 构建成功!")

    # 执行验证测试
    executor = SafeProcessExecutor(timeout_seconds=45, memory_limit_mb=512)

    # 准备环境变量 - 针对Tokio运行时优化
    env = {
        "RUST_LOG": "warn",  # 减少日志输出
        "RUST_BACKTRACE": "1",  # 启用堆栈跟踪
        "TOKIO_WORKER_THREADS": "2",  # 限制Tokio工作线程数
        "TOKIO_THREAD_STACK_SIZE": "2097152",  # 2MB栈大小
        "RUSTFLAGS": "-C target-cpu=native",  # 本机优化
    }

    # 执行验证命令
    verify_cmd = ["cargo", "run", "--bin", "verify-libafl", "--release"]

    print("\n🚀 开始安全执行LibAFL验证...")
    result = executor.execute_command(verify_cmd, cwd=str(engine_dir), env=env)

    # 打印详细报告
    print_detailed_report(result)

    # 保存结果到文件
    report_file = engine_dir / "verification_report.json"
    try:
        with open(report_file, "w", encoding="utf-8") as f:
            json.dump(asdict(result), f, indent=2, ensure_ascii=False)
        print(f"\n💾 详细报告已保存到: {report_file}")
    except Exception as e:
        print(f"⚠️ 保存报告失败: {e}")

    # 返回状态码
    if result.success:
        print("\n🎉 LibAFL验证测试安全完成!")
        return 0
    print(f"\n⚠️ LibAFL验证测试遇到问题 (退出代码: {result.exit_code})")
    if result.crash_detected:
        print("💥 检测到崩溃，但已被安全拦截")
    return result.exit_code if result.exit_code != 0 else 1


if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断执行")
        sys.exit(130)
    except Exception as e:
        print(f"\n❌ 执行过程中发生未处理的异常: {e}")
        import traceback

        traceback.print_exc()
        sys.exit(1)
