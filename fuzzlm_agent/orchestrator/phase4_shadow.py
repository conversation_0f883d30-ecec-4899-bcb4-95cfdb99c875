"""
Phase 4: Shadow Validation and Challenger Testing
=================================================

This module handles shadow validation as described in docs/workflow.md Phase 4.
It generates challenger strategies using LLM, spawns shadow fuzzers with limited
resources, and decides whether to promote shadow to champion based on performance.

This is a standalone, simplified implementation focused on research prototype
principles without event-driven complexity.
"""

from __future__ import annotations

import logging
from dataclasses import dataclass
from datetime import datetime, timedelta, timezone
from typing import TYPE_CHECKING, Any

from fuzzlm_agent.core import (
    PerformanceMetrics as CorePerformanceMetrics,
)
from fuzzlm_agent.core import (
    TelemetryAnalyzer,
)
from fuzzlm_agent.prompts import PromptManager

if TYPE_CHECKING:
    from fuzzlm_agent.infrastructure.litellm_client import LiteLLMClient
    from fuzzlm_agent.infrastructure.shared_memory import TelemetryReader
    from fuzzlm_agent.orchestrator.campaign_orchestrator import CampaignContext

logger = logging.getLogger(__name__)

# Initialize prompt manager (global instance for performance)
_prompt_manager = PromptManager()


@dataclass
class ChallengerStrategy:
    """A challenger strategy generated by LLM."""

    strategy_id: str
    name: str
    description: str
    components: dict[str, Any]
    expected_improvements: list[str]
    generation_reasoning: str
    confidence: float


@dataclass
class PerformanceMetrics:
    """Performance metrics for a fuzzer instance."""

    executions: int
    new_paths: int
    coverage_percentage: float
    crashes: int
    efficiency: float  # new_paths per 10k executions


async def phase4_shadow_test(
    ctx: CampaignContext,  # CampaignContext
    runtime_client: Any,  # RuntimeClient
    llm_client: LiteLLMClient,  # LiteLLMClient
    telemetry_reader: TelemetryReader | None,  # TelemetryReader
) -> Any:  # Returns updated CampaignContext
    """
    Execute shadow validation phase.

    This function:
    1. Generates challenger strategy based on phase 3 analysis
    2. Spawns shadow fuzzer from champion snapshot (10-20% resources)
    3. Runs both champion and shadow in parallel for test period
    4. Compares normalized efficiency metrics
    5. Decides whether to promote shadow to champion
    6. Updates context with results

    Args:
        ctx: Campaign context with phase 3 results
        runtime_client: Client for controlling fuzzing runtime
        llm_client: LLM client for strategy generation
        telemetry_reader: Reader for telemetry data stream

    Returns:
        Updated campaign context with shadow test results
    """
    logger.info("Phase 4: Starting shadow validation")

    # Ensure metadata field exists
    if not hasattr(ctx, "metadata"):
        ctx.metadata = {}

    # Check if shadow testing is needed
    if not ctx.metadata.get("needs_adjustment", False):
        logger.info("No adjustment needed, skipping shadow validation")
        ctx.metadata["phase4_skipped"] = True
        return ctx

    # Generate challenger strategy
    logger.info("Generating challenger strategy with LLM")
    challenger = await _generate_challenger_strategy(llm_client, ctx)

    if not challenger:
        logger.error("Failed to generate challenger strategy")
        ctx.metadata["phase4_error"] = "Strategy generation failed"
        return ctx

    # Store challenger strategy
    ctx.metadata["challenger_strategy"] = {
        "name": challenger.name,
        "description": challenger.description,
        "confidence": challenger.confidence,
        "expected_improvements": challenger.expected_improvements,
    }

    # Spawn shadow fuzzer
    logger.info(f"Spawning shadow fuzzer with strategy: {challenger.name}")
    try:
        shadow_id = await runtime_client.spawn_shadow(
            champion_id=ctx.metadata.get("champion_fuzzer_id"),
            strategy=challenger.components,
            resource_allocation=0.15,  # 15% resources
        )
        ctx.metadata["shadow_fuzzer_id"] = shadow_id
        logger.info(f"Shadow fuzzer spawned with ID: {shadow_id}")
    except Exception as e:
        logger.error(f"Failed to spawn shadow fuzzer: {e}")
        ctx.metadata["phase4_error"] = str(e)
        return ctx

    # Run parallel performance test (30 minutes)
    test_duration = timedelta(minutes=30)
    logger.info(f"Running parallel performance test for {test_duration}")

    champion_fuzzer_id = ctx.metadata.get("champion_fuzzer_id")
    if not champion_fuzzer_id:
        logger.error("No champion fuzzer ID found in metadata")
        ctx.metadata["phase4_error"] = "Missing champion fuzzer ID"
        return ctx

    champion_metrics, shadow_metrics = await _parallel_performance_test(
        runtime_client,
        telemetry_reader,
        str(champion_fuzzer_id),
        shadow_id,
        test_duration,
    )

    # Store performance metrics
    ctx.metadata["shadow_test_results"] = {
        "champion_metrics": _metrics_to_dict(champion_metrics),
        "shadow_metrics": _metrics_to_dict(shadow_metrics),
        "test_duration_minutes": test_duration.total_seconds() / 60,
    }

    # Decide on promotion
    should_promote = _should_promote_shadow(champion_metrics, shadow_metrics)
    ctx.metadata["shadow_promoted"] = should_promote

    if should_promote:
        logger.info("🎉 Shadow performance superior, promoting to champion")
        try:
            # Promote shadow to champion
            new_champion_id = await runtime_client.promote_shadow(
                champion_id=ctx.metadata.get("champion_fuzzer_id"),
                shadow_id=shadow_id,
            )

            # Update context
            ctx.metadata["old_champion_id"] = ctx.metadata.get("champion_fuzzer_id")
            ctx.metadata["champion_fuzzer_id"] = new_champion_id
            ctx.strategy = challenger.components
            if "strategy_changes" not in ctx.final_metrics:
                ctx.final_metrics["strategy_changes"] = 0
            ctx.final_metrics["strategy_changes"] += 1

            logger.info(f"Promotion successful. New champion: {new_champion_id}")

        except Exception as e:
            logger.error(f"Failed to promote shadow: {e}")
            ctx.metadata["promotion_error"] = str(e)
            # Clean up shadow
            await _cleanup_shadow(runtime_client, shadow_id)
    else:
        logger.info("Shadow performance not superior, keeping champion")
        # Clean up shadow
        await _cleanup_shadow(runtime_client, shadow_id)

    # Update phase completion
    ctx.metadata["phase4_completed"] = datetime.now(timezone.utc).isoformat()

    return ctx


async def _generate_challenger_strategy(
    llm_client: Any,
    ctx: Any,
) -> ChallengerStrategy | None:
    """Generate challenger strategy based on phase 3 analysis."""

    # Get bottleneck and current strategy info
    bottleneck = ctx.metadata.get("primary_bottleneck", "coverage stagnation")
    current_strategy = ctx.strategy
    state_analyses = ctx.metadata.get("state_analyses", [])

    # Get most recent state analysis
    recent_analysis = state_analyses[-1] if state_analyses else {}
    suggested_actions = recent_analysis.get("suggested_actions", [])

    try:
        # Use PromptManager to generate the strategy optimization prompt
        prompt = _prompt_manager.get_prompt(
            "strategy.optimization",
            target_path=ctx.config.target_path,
            current_strategy_name=(
                current_strategy.name if current_strategy else "Default"
            ),
            elapsed_hours=ctx.elapsed_hours,
            total_executions=ctx.total_executions,
            final_coverage=ctx.final_coverage,
            unique_crashes=ctx.unique_crashes,
            bottleneck=bottleneck,
            suggested_actions=suggested_actions,
            current_strategy_config=(
                _format_strategy(current_strategy)
                if current_strategy
                else "Default configuration"
            ),
        )

    except Exception as e:
        logger.error(f"Failed to generate strategy optimization prompt: {e}")
        # Fallback to basic prompt if template fails
        prompt = f"""Generate an optimized challenger strategy for {ctx.config.target_path}.

Current strategy: {current_strategy.name if current_strategy else "Default"}
Duration: {ctx.elapsed_hours:.1f} hours
Bottleneck: {bottleneck}

Respond in JSON format with name, description, components, expected_improvements, reasoning, and confidence."""

    try:
        # 使用LiteLLMClient同步API
        response_text = llm_client.generate(
            prompt=prompt,
            max_tokens=800,
            temperature=0.4,  # Moderate creativity
        )

        # Parse response using json_repair
        import json_repair

        strategy_data = json_repair.loads(response_text)

        if not isinstance(strategy_data, dict):
            msg = "JSON parsing failed: response is not a JSON object"
            raise ValueError(msg)

        # 确保 strategy_data 不为 None
        if strategy_data is None:
            msg = "Parsed strategy data is None"
            raise ValueError(msg)

        # Create challenger strategy
        strategy_id = (
            f"challenger_{datetime.now(timezone.utc).strftime('%Y%m%d_%H%M%S')}"
        )

        return ChallengerStrategy(
            strategy_id=strategy_id,
            name=strategy_data.get("name", "Generated Challenger"),
            description=strategy_data.get("description", ""),
            components=strategy_data.get("components", {}),
            expected_improvements=strategy_data.get("expected_improvements", []),
            generation_reasoning=strategy_data.get("reasoning", ""),
            confidence=float(strategy_data.get("confidence", 0.5)),
        )

    except Exception as e:
        logger.error(f"Failed to generate challenger strategy: {e}")
        return None


def _format_strategy(strategy: Any) -> str:
    """Format strategy for display."""
    if not strategy or not hasattr(strategy, "components"):
        return "No components"

    lines = []
    components = strategy.components if isinstance(strategy.components, dict) else {}

    for key, value in components.items():
        if isinstance(value, dict):
            lines.append(f"{key}:")
            for k, v in value.items():
                lines.append(f"  {k}: {v}")
        else:
            lines.append(f"{key}: {value}")

    return "\n".join(lines)


async def _parallel_performance_test(
    runtime_client: Any,
    telemetry_reader: TelemetryReader | None,  # Can be None
    champion_id: str,
    shadow_id: str,
    duration: timedelta,
) -> tuple[PerformanceMetrics, PerformanceMetrics]:
    """Run parallel performance test and collect metrics."""

    # Ensure we have telemetry reader for data plane access
    if telemetry_reader is None:
        msg = "Telemetry reader is required for shadow performance testing"
        raise ValueError(msg)

    # Initialize telemetry analyzer
    telemetry_analyzer = TelemetryAnalyzer()

    # Analyze telemetry streams for both fuzzers
    logger.info("Analyzing parallel performance using telemetry data plane")

    champion_perf: CorePerformanceMetrics | None = None
    shadow_perf: CorePerformanceMetrics | None = None

    # Define callback to capture metrics by instance ID
    captured_metrics = {}

    async def capture_metrics_callback(
        metrics: CorePerformanceMetrics, anomalies: list[str]
    ) -> None:
        """Callback to capture metrics by instance ID."""
        instance_id = getattr(metrics, "instance_id", None)
        if instance_id:
            captured_metrics[instance_id] = metrics
            if anomalies:
                logger.warning(f"Anomalies detected for {instance_id}: {anomalies}")

    # Run telemetry analysis for the test duration
    try:
        metrics_history = await telemetry_analyzer.analyze_telemetry_stream(
            telemetry_reader,
            duration,
            callback=capture_metrics_callback,
        )

        # Extract final metrics for each fuzzer
        for metrics in metrics_history:
            instance_id = getattr(metrics, "instance_id", None)
            if instance_id == champion_id:
                champion_perf = metrics
            elif instance_id == shadow_id:
                shadow_perf = metrics

        # If we didn't get metrics from history, check captured ones
        if champion_perf is None and champion_id in captured_metrics:
            champion_perf = captured_metrics[champion_id]
        if shadow_perf is None and shadow_id in captured_metrics:
            shadow_perf = captured_metrics[shadow_id]

    except Exception as e:
        logger.error(f"Failed to analyze telemetry stream: {e}")
        # Fallback to empty metrics
        champion_perf = None
        shadow_perf = None

    # Convert core metrics to local PerformanceMetrics
    champion_metrics = (
        _convert_core_metrics(champion_perf)
        if champion_perf
        else PerformanceMetrics(
            executions=0,
            new_paths=0,
            coverage_percentage=0.0,
            crashes=0,
            efficiency=0.0,
        )
    )

    shadow_metrics = (
        _convert_core_metrics(shadow_perf)
        if shadow_perf
        else PerformanceMetrics(
            executions=0,
            new_paths=0,
            coverage_percentage=0.0,
            crashes=0,
            efficiency=0.0,
        )
    )

    # Log final metrics
    logger.info(
        f"Performance test completed - "
        f"Champion efficiency: {champion_metrics.efficiency:.3f}, "
        f"Shadow efficiency: {shadow_metrics.efficiency:.3f}"
    )

    return champion_metrics, shadow_metrics


def _convert_core_metrics(core_metrics: CorePerformanceMetrics) -> PerformanceMetrics:
    """Convert core PerformanceMetrics to local PerformanceMetrics."""
    return PerformanceMetrics(
        executions=core_metrics.total_executions,
        new_paths=core_metrics.new_paths,
        coverage_percentage=core_metrics.coverage_percentage,
        crashes=core_metrics.crashes_found,
        efficiency=core_metrics.efficiency_score
        * 10000,  # Scale to match expected units
    )


def _should_promote_shadow(
    champion: PerformanceMetrics, shadow: PerformanceMetrics
) -> bool:
    """Decide whether shadow should be promoted to champion."""

    # Normalize for resource allocation difference
    # Shadow gets ~15% resources, so multiply its metrics by ~6.67
    resource_factor = 6.67
    normalized_shadow_efficiency = shadow.efficiency * resource_factor

    # Promotion criteria:
    # 1. Shadow efficiency is significantly better (>20% improvement)
    # 2. OR shadow found crashes while champion didn't
    # 3. AND shadow has reasonable execution count (>1000)

    efficiency_improvement = (normalized_shadow_efficiency - champion.efficiency) / max(
        champion.efficiency, 0.001
    )

    has_crash_advantage = shadow.crashes > 0 and champion.crashes == 0
    has_sufficient_executions = shadow.executions > 1000

    should_promote = has_sufficient_executions and (
        efficiency_improvement > 0.2 or has_crash_advantage
    )

    logger.info(
        f"Promotion decision - Efficiency improvement: {efficiency_improvement:.1%}, "
        f"Crash advantage: {has_crash_advantage}, "
        f"Decision: {'PROMOTE' if should_promote else 'KEEP CHAMPION'}"
    )

    return should_promote


def _metrics_to_dict(metrics: PerformanceMetrics) -> dict[str, Any]:
    """Convert metrics to dictionary for storage."""
    return {
        "executions": metrics.executions,
        "new_paths": metrics.new_paths,
        "coverage_percentage": metrics.coverage_percentage,
        "crashes": metrics.crashes,
        "efficiency": metrics.efficiency,
    }


async def _cleanup_shadow(runtime_client: Any, shadow_id: str) -> None:
    """Clean up shadow fuzzer instance."""
    try:
        await runtime_client.stop_fuzzer(shadow_id)
        logger.info(f"Shadow fuzzer {shadow_id} cleaned up")
    except Exception as e:
        logger.error(f"Failed to clean up shadow {shadow_id}: {e}")
